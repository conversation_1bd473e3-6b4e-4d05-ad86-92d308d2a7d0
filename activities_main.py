import mysql.connector
from mysql.connector import <PERSON>rror
import pandas as pd
from datetime import datetime

class ActivitiesDataRetriever:
    def __init__(self, host='localhost', database='forge', user='root', password=''):
        """
        Initialize database connection parameters
        
        Args:
            host (str): MySQL server host (default: localhost)
            database (str): Database name (default: forge)
            user (str): MySQL username (default: root)
            password (str): MySQL password (default: empty)
        """
        self.host = host
        self.database = database
        self.user = user
        self.password = password
        self.connection = None
    
    def connect(self):
        """Establish connection to MySQL database"""
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                database=self.database,
                user=self.user,
                password=self.password
            )
            
            if self.connection.is_connected():
                print(f"Successfully connected to MySQL database: {self.database}")
                return True
                
        except Error as e:
            print(f"Error connecting to MySQL: {e}")
            return False
    
    def disconnect(self):
        """Close database connection"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            print("MySQL connection closed")
    
    def get_all_activity_providers_by_date(self, start_date, end_date):
        """
        Retrieve all activity providers with their information within specified date range
        Each booking status will be a separate row
        
        Args:
            start_date (str): Start date in YYYY-MM-DD format
            end_date (str): End date in YYYY-MM-DD format
            
        Returns:
            list: List of dictionaries containing activity provider data by status
        """
        
        query = """
        WITH trip_parts_providers AS (
            SELECT 
                tp.trip_service_id,
                tp.provider_id,
                GROUP_CONCAT(DISTINCT CONCAT(p.first_name, ' ', p.last_name) SEPARATOR ', ') AS all_providers,
                p.external_account_id,
                p.provider_number,
                p.first_name,
                p.last_name
            FROM trip_parts tp
            JOIN providers p ON tp.provider_id = p.id
            GROUP BY tp.trip_service_id, tp.provider_id, p.external_account_id, p.provider_number, p.first_name, p.last_name
        )
        SELECT 
            p.external_account_id as Datev_Expense_account,
            p.id as provider_id,
            p.provider_number as provider_number,
            p.first_name as Providers_first_name,
            p.last_name as providers_last_name,
            CONCAT(p.first_name, ' ', p.last_name) AS provider_full_name,
            ta.booking_status,
            COUNT(DISTINCT ta.id) as activity_booking_count,
            COUNT(DISTINCT tp.id) as parts_count,
            SUM(tp.cost)/100 as total_cost_eur,
            SUM((tp.cost/100) * 1.08) as total_cost_usd,
            GROUP_CONCAT(DISTINCT a.name ORDER BY a.name SEPARATOR ', ') as activities,
            GROUP_CONCAT(DISTINCT l.name ORDER BY l.name SEPARATOR ', ') as locations
        FROM 
            trips t
        INNER JOIN trip_activities ta ON t.id = ta.trip_id
        LEFT JOIN activities a ON ta.activity_id = a.id
        LEFT JOIN locations l ON a.location_id = l.id
        LEFT JOIN trip_parts tp ON ta.id = tp.trip_service_id
        LEFT JOIN providers p ON tp.provider_id = p.id
        WHERE 
            p.id IS NOT NULL
            AND ta.date >= %s
            AND ta.date <= %s
            AND t.is_booker_version = 1 
            AND ta.deleted_at IS NULL
            AND t.deleted_at IS NULL
        GROUP BY 
            p.id, 
            p.external_account_id, 
            p.provider_number, 
            p.first_name, 
            p.last_name,
            ta.booking_status
        ORDER BY p.provider_number, ta.booking_status, total_cost_eur DESC
        """
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, (start_date, end_date))
            results = cursor.fetchall()
            cursor.close()
            
            print(f"Retrieved {len(results)} activity provider status records")
            return results
            
        except Error as e:
            print(f"Error executing query: {e}")
            return []
    
    def get_activities_detailed_data(self, start_date, end_date):
        """
        Get detailed activities data for all providers within date range
        
        Args:
            start_date (str): Start date in YYYY-MM-DD format
            end_date (str): End date in YYYY-MM-DD format
            
        Returns:
            list: List of detailed activity records
        """
        
        query = """
        WITH activity_details AS (
            SELECT 
                tp.trip_service_id,
                tp.booking_status,
                ap.label,
                ap.type,
                (tp.cost/100) as cost_EUR,
                (tp.cost/100) * 1.08 AS cost_usd,
                GROUP_CONCAT(DISTINCT CONCAT(parts_prov.first_name, ' ', parts_prov.last_name) SEPARATOR ', ') AS providers
            FROM trip_parts tp
            JOIN providers parts_prov ON tp.provider_id = parts_prov.id
            JOIN activity_parts ap ON tp.part_id = ap.id
            GROUP BY 
                tp.trip_service_id,
                tp.booking_status,
                ap.label,
                ap.type,
                tp.cost
        ),
        trip_parts_providers AS (
            SELECT 
                tp.trip_service_id,
                tp.provider_id,
                p.external_account_id,
                p.provider_number,
                GROUP_CONCAT(DISTINCT CONCAT(p.first_name, ' ', p.last_name) SEPARATOR ', ') AS all_providers
            FROM trip_parts tp
            JOIN providers p ON tp.provider_id = p.id
            GROUP BY tp.trip_service_id, tp.provider_id, p.external_account_id, p.provider_number
        )
        SELECT 
            ta.date as start_date,
            t.deal_number,
            t.trip_name,
            t.travelers AS travellers,
            a.name as Activity,
            GROUP_CONCAT(DISTINCT 
                CONCAT(
                    IFNULL(ad.label, 'No Label'),
                    ' (',
                    IFNULL(ad.type, 'No Type'),
                    '): ',
                    ' : COST ( EUR - ', IFNULL(ad.cost_EUR, 0),
                    ' , USD - ', IFNULL(ad.cost_usd, 0), ')'
                )
                ORDER BY ad.label, ad.type
                SEPARATOR ', ') AS Activity_Details,
            l.name AS location_name,
            ta.booking_status,
            ad.booking_status as part_booking_status,
            tpp.provider_id,
            tpp.external_account_id as Datev_Expense_account,
            tpp.provider_number,
            tpp.all_providers as provider
        FROM 
            trips t
        INNER JOIN trip_activities ta ON t.id = ta.trip_id
        LEFT JOIN activities a ON ta.activity_id = a.id
        LEFT JOIN locations l ON a.location_id = l.id
        LEFT JOIN activity_details ad ON ta.id = ad.trip_service_id
        LEFT JOIN trip_parts_providers tpp ON ta.id = tpp.trip_service_id
        WHERE 
            ta.date >= %s
            AND ta.date <= %s
            AND t.is_booker_version = 1 
            AND ta.deleted_at IS NULL
            AND t.deleted_at IS NULL
            AND tpp.provider_id IS NOT NULL
        GROUP BY 
            t.deal_number,
            t.trip_name,
            t.travelers,
            a.name,
            ta.date,
            ta.booking_status,
            l.name,
            tpp.provider_id,
            tpp.external_account_id,
            tpp.provider_number,
            tpp.all_providers,
            ad.booking_status
        ORDER BY 
            ta.date,
            t.trip_name,
            t.deal_number,
            l.name,
            a.name,
            ta.booking_status
        """
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, (start_date, end_date))
            results = cursor.fetchall()
            cursor.close()
            
            print(f"Retrieved {len(results)} detailed activity records")
            return results
            
        except Error as e:
            print(f"Error executing query: {e}")
            return []


def main():
    # Initialize the database connection
    # Update these parameters according to your MySQL configuration
    db_retriever = ActivitiesDataRetriever(
        host='localhost',
        database='forge',
        user='root',  # Update with your MySQL username
        password=''   # Update with your MySQL password
    )
    
    # Connect to database
    if not db_retriever.connect():
        return
    
    try:
        # Set your date range
        start_date = '2024-01-01'
        end_date = '2024-12-31'
        
        print(f"\nRetrieving activity provider data from {start_date} to {end_date}...")
        
        # Get all activity providers with summary information
        providers_data = db_retriever.get_all_activity_providers_by_date(start_date, end_date)
        
        # Display results
        if providers_data:
            print("\n" + "="*80)
            print("ACTIVITY PROVIDERS SUMMARY")
            print("="*80)
            
            for provider in providers_data:
                print(f"\nProvider ID: {provider['provider_id']}")
                print(f"Provider Number: {provider['provider_number']}")
                provider_name = f"{provider['Providers_first_name'] or ''} {provider['providers_last_name'] or ''}".strip()
                if not provider_name:
                    provider_name = "Name not available"
                print(f"Name: {provider_name}")
                external_account = provider['Datev_Expense_account'] or "Not available"
                print(f"External Account ID: {external_account}")
                activity_count = provider['activity_booking_count'] or 0
                parts_count = provider['parts_count'] or 0
                print(f"Activity Bookings: {activity_count}")
                print(f"Parts Count: {parts_count}")
                total_eur = provider['total_cost_eur'] or 0
                total_usd = provider['total_cost_usd'] or 0
                print(f"Total Cost: €{total_eur:.2f} / ${total_usd:.2f}")
                activities_list = provider['activities'] or "No activities listed"
                locations_list = provider['locations'] or "No locations listed"
                # FIXED: Changed from 'booking_statuses' to 'booking_status'
                booking_status = provider['booking_status'] or "No status listed"
                print(f"Activities: {activities_list}")
                print(f"Locations: {locations_list}")
                print(f"Booking Status: {booking_status}")
                print("-" * 50)
        
        # Get detailed activities data
        print("\n" + "="*80)
        print("DETAILED ACTIVITIES DATA")
        print("="*80)
        
        detailed_data = db_retriever.get_activities_detailed_data(start_date, end_date)
        
        if detailed_data:
            print(f"Total detailed records: {len(detailed_data)}")
            
            # Show first 5 records as sample
            for i, record in enumerate(detailed_data[:5]):
                print(f"\nRecord {i+1}:")
                print(f"Trip: {record['trip_name']} ({record['deal_number']})")
                print(f"Date: {record['start_date']}")
                provider_name = record['provider'] or "Provider not available"
                provider_id = record['provider_id'] or "N/A"
                provider_number = record['provider_number'] or "N/A"
                external_account = record['Datev_Expense_account'] or "N/A"
                activity_name = record['Activity'] or "Activity not specified"
                location_name = record['location_name'] or "Location not specified"
                travelers = record['travellers'] or "N/A"
                booking_status = record['booking_status'] or "Status not available"
                
                print(f"Activity: {activity_name}")
                print(f"Location: {location_name}")
                print(f"Travelers: {travelers}")
                print(f"Provider: {provider_name} (ID: {provider_id})")
                print(f"Provider Number: {provider_number}")
                print(f"External Account: {external_account}")
                print(f"Status: {booking_status}")
                if record['Activity_Details']:
                    print(f"Details: {record['Activity_Details'][:100]}...")
                print("-" * 30)
            
            if len(detailed_data) > 5:
                print(f"... and {len(detailed_data) - 5} more records")
        
        # Save to CSV files
        if providers_data:
            # Save provider summary by status
            df_summary = pd.DataFrame(providers_data)
            summary_filename = f"activities_providers_by_status_{start_date}_to_{end_date}.csv"
            df_summary.to_csv(summary_filename, index=False)
            print(f"\nProvider summary by status saved to {summary_filename}")
        
        if detailed_data:
            # Save detailed data
            df_detailed = pd.DataFrame(detailed_data)
            detailed_filename = f"activities_detailed_{start_date}_to_{end_date}.csv"
            df_detailed.to_csv(detailed_filename, index=False)
            print(f"Detailed data saved to {detailed_filename}")
            
    except Exception as e:
        print(f"An error occurred: {e}")
    
    finally:
        # Always close the connection
        db_retriever.disconnect()


if __name__ == "__main__":
    main()