import mysql.connector
from mysql.connector import <PERSON>rror
import pandas as pd
from datetime import datetime

class TransportDataRetriever:
    def __init__(self, host='localhost', database='forge', user='root', password=''):
        """
        Initialize database connection parameters
        
        Args:
            host (str): MySQL server host (default: localhost)
            database (str): Database name (default: forge)
            user (str): MySQL username (default: root)
            password (str): MySQL password (default: empty)
        """
        self.host = host
        self.database = database
        self.user = user
        self.password = password
        self.connection = None
    
    def connect(self):
        """Establish connection to MySQL database"""
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                database=self.database,
                user=self.user,
                password=self.password
            )
            
            if self.connection.is_connected():
                print(f"Successfully connected to MySQL database: {self.database}")
                return True
                
        except Error as e:
            print(f"Error connecting to MySQL: {e}")
            return False
    
    def disconnect(self):
        """Close database connection"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            print("MySQL connection closed")
    
    def get_all_transport_providers_by_date(self, start_date, end_date):
        """
        Retrieve all transport providers with their information within specified date range
        Each booking status will be a separate row
        
        Args:
            start_date (str): Start date in YYYY-MM-DD format
            end_date (str): End date in YYYY-MM-DD format
            
        Returns:
            list: List of dictionaries containing transport provider data by status
        """
        
        query = """
        SELECT 
            p.external_account_id as Datev_Expense_account,
            p.id as provider_id,
            p.provider_number as provider_number,
            p.first_name as Providers_first_name,
            p.last_name as providers_last_name,
            CONCAT(p.first_name, ' ', p.last_name) AS provider_full_name,
            tt.booking_status,
            COUNT(tt.id) as transport_booking_count,
            SUM(tt.cost)/100 as total_cost_eur,
            SUM((tt.cost/100) * 1.08) as total_cost_usd,
            GROUP_CONCAT(DISTINCT lo.name ORDER BY lo.name SEPARATOR ', ') as origin_locations,
            GROUP_CONCAT(DISTINCT ld.name ORDER BY ld.name SEPARATOR ', ') as destination_locations
        FROM 
            trips t
        INNER JOIN trip_transports tt ON t.id = tt.trip_id
        LEFT JOIN locations lo ON tt.location_origin_id = lo.id
        LEFT JOIN locations ld ON tt.location_destination_id = ld.id
        LEFT JOIN providers p ON tt.provider_id = p.id
        WHERE 
            p.id IS NOT NULL
            AND t.is_booker_version = 1 
            AND t.deleted_at IS NULL 
            AND tt.deleted_at IS NULL 
            AND tt.start_date >= %s
            AND tt.start_date <= %s
        GROUP BY 
            p.id, 
            p.external_account_id, 
            p.provider_number, 
            p.first_name, 
            p.last_name,
            tt.booking_status
        ORDER BY p.provider_number, tt.booking_status, total_cost_eur DESC
        """
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, (start_date, end_date))
            results = cursor.fetchall()
            cursor.close()
            
            print(f"Retrieved {len(results)} transport provider status records")
            return results
            
        except Error as e:
            print(f"Error executing query: {e}")
            return []
    
    def get_transport_detailed_data(self, start_date, end_date):
        """
        Get detailed transport data for all providers within date range
        
        Args:
            start_date (str): Start date in YYYY-MM-DD format
            end_date (str): End date in YYYY-MM-DD format
            
        Returns:
            list: List of detailed transport records
        """
        
        query = """
        SELECT 
            t.trip_name,
            t.deal_number,
            tt.start_date,
            tt.cost/100 AS cost_eur,
            (tt.cost/100) * 1.08 AS cost_usd,
            lo.name AS location_origin,
            ld.name AS location_destination,
            tt.booking_status,
            p.external_account_id as Datev_Expense_account,
            p.id as provider_id,
            p.provider_number as provider_number,
            CONCAT(p.first_name, ' ', p.last_name) AS provider
        FROM 
            trips t 
        INNER JOIN trip_transports tt ON t.id = tt.trip_id 
        LEFT JOIN locations lo ON tt.location_origin_id = lo.id
        LEFT JOIN locations ld ON tt.location_destination_id = ld.id
        LEFT JOIN providers p ON tt.provider_id = p.id 
        WHERE 
            p.id IS NOT NULL
            AND t.is_booker_version = 1 
            AND t.deleted_at IS NULL 
            AND tt.deleted_at IS NULL 
            AND tt.start_date >= %s
            AND tt.start_date <= %s
        ORDER BY t.deal_number, tt.start_date
        """
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, (start_date, end_date))
            results = cursor.fetchall()
            cursor.close()
            
            print(f"Retrieved {len(results)} detailed transport records")
            return results
            
        except Error as e:
            print(f"Error executing query: {e}")
            return []


def main():
    # Initialize the database connection
    # Update these parameters according to your MySQL configuration
    db_retriever = TransportDataRetriever(
        host='localhost',
        database='forge',
        user='root',  # Update with your MySQL username
        password=''   # Update with your MySQL password
    )
    
    # Connect to database
    if not db_retriever.connect():
        return
    
    try:
        # Set your date range
        start_date = '2024-01-01'
        end_date = '2024-12-31'
        
        print(f"\nRetrieving transport provider data from {start_date} to {end_date}...")
        
        # Get all transport providers with summary information
        providers_data = db_retriever.get_all_transport_providers_by_date(start_date, end_date)
        
        # Display results
        if providers_data:
            print("\n" + "="*80)
            print("TRANSPORT PROVIDERS BY STATUS SUMMARY")
            print("="*80)
            
            for provider in providers_data:
                provider_name = f"{provider['Providers_first_name'] or ''} {provider['providers_last_name'] or ''}".strip()
                if not provider_name:
                    provider_name = "Name not available"
                
                total_eur = provider['total_cost_eur'] or 0
                total_usd = provider['total_cost_usd'] or 0
                external_account = provider['Datev_Expense_account'] or "Not available"
                origin_locations = provider['origin_locations'] or "No origins listed"
                destination_locations = provider['destination_locations'] or "No destinations listed"
                booking_count = provider['transport_booking_count'] or 0
                booking_status = provider['booking_status'] or "Status not available"
                
                print(f"\nProvider ID: {provider['provider_id']}")
                print(f"Provider Number: {provider['provider_number']}")
                print(f"Name: {provider_name}")
                print(f"External Account ID: {external_account}")
                print(f"Booking Status: {booking_status}")
                print(f"Bookings with this Status: {booking_count}")
                print(f"Total Cost for this Status: €{total_eur:.2f} / ${total_usd:.2f}")
                print(f"Origin Locations: {origin_locations}")
                print(f"Destination Locations: {destination_locations}")
                print("-" * 50)
        
        # Get detailed transport data
        print("\n" + "="*80)
        print("DETAILED TRANSPORT DATA")
        print("="*80)
        
        detailed_data = db_retriever.get_transport_detailed_data(start_date, end_date)
        
        if detailed_data:
            print(f"Total detailed records: {len(detailed_data)}")
            
            # Show first 5 records as sample
            for i, record in enumerate(detailed_data[:5]):
                print(f"\nRecord {i+1}:")
                print(f"Trip: {record['trip_name']} ({record['deal_number']})")
                print(f"Date: {record['start_date']}")
                print(f"Route: {record['location_origin']} → {record['location_destination']}")
                print(f"Cost: €{record['cost_eur']:.2f}")
                print(f"Provider: {record['provider']} (ID: {record['provider_id']})")
                print(f"Status: {record['booking_status']}")
                print("-" * 30)
            
            if len(detailed_data) > 5:
                print(f"... and {len(detailed_data) - 5} more records")
        
        # Save to CSV files
        if providers_data:
            # Save provider summary by status
            df_summary = pd.DataFrame(providers_data)
            summary_filename = f"transport_providers_by_status_{start_date}_to_{end_date}.csv"
            df_summary.to_csv(summary_filename, index=False)
            print(f"\nProvider summary by status saved to {summary_filename}")
        
        if detailed_data:
            # Save detailed data
            df_detailed = pd.DataFrame(detailed_data)
            detailed_filename = f"transport_detailed_{start_date}_to_{end_date}.csv"
            df_detailed.to_csv(detailed_filename, index=False)
            print(f"Detailed data saved to {detailed_filename}")
            
    except Exception as e:
        print(f"An error occurred: {e}")
    
    finally:
        # Always close the connection
        db_retriever.disconnect()


if __name__ == "__main__":
    main()